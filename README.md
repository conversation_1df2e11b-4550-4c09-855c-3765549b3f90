# 远程文件备份工具

一个功能强大的图形化远程文件备份工具，支持服务器间传输和文件压缩功能。

## 🚀 核心功能

- 🗜️ **文件压缩传输**: 支持tar.gz格式压缩，显著减少传输时间
- 🔄 **服务器间传输**: 支持从一个服务器直接传输到另一个服务器
- 🖥️ **灵活端点配置**: 源端和目标端可以是本地或任意远程服务器
- 🔐 **安全连接**: 使用SSH协议，密码加密存储
- 🗂️ **图形化浏览**: 远程目录浏览，支持目录导航
- 📊 **智能分析**: 传输前分析，提供优化建议和时间估算
- 🎛️ **完整管理**: 图形化服务器配置管理
- 📝 **详细日志**: 实时操作日志和进度显示

## 系统要求

- Python 3.7+
- Windows 10/11 (主要测试平台)
- 网络连接到目标Linux服务器

## 安装和运行

### 方法1: 源码运行

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd directory-transfer
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   ```

3. **激活虚拟环境**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

4. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

5. **运行程序**
   ```bash
   python main.py
   ```

### 方法2: 打包成可执行文件

1. **确保已安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行打包脚本**
   ```bash
   # Windows
   build.bat
   
   # 或者直接运行Python脚本
   python build.py
   ```

3. **运行可执行文件**
   ```bash
   dist\RemoteBackupTool.exe
   ```

## 使用说明

### 1. 添加服务器配置

首次使用时，需要添加SSH服务器配置：

1. 点击"添加服务器"按钮
2. 填写服务器信息：
   - 服务器名称：自定义名称
   - 主机地址：服务器IP或域名
   - 端口：SSH端口（默认22）
   - 用户名：SSH用户名
   - 密码：SSH密码
3. 点击"确定"保存配置

### 2. 连接服务器

1. 在下拉列表中选择服务器
2. 点击"测试连接"验证连接
3. 连接成功后状态显示为"已连接"

### 3. 配置传输端点

#### 源端点配置
1. 选择端点类型：本地或远程服务器
2. 如果选择远程服务器：
   - 从下拉列表选择服务器
   - 点击"连接"按钮建立连接
3. 设置源路径：
   - 本地：点击"浏览"选择目录
   - 远程：点击"浏览"使用图形化目录浏览器

#### 目标端点配置
1. 选择端点类型：本地或远程服务器
2. 配置方法同源端点
3. 设置目标路径

### 4. 传输选项设置
1. **启用压缩**: 勾选后使用tar.gz压缩传输
2. **压缩级别**: 调整1-9级压缩级别
3. **传输验证**: 可选的传输后验证

### 5. 执行传输
1. 点击"分析传输"查看传输计划和建议
2. 点击"开始传输"执行备份操作
3. 实时查看传输进度和日志

### 6. 支持的传输模式

本工具支持四种传输模式：

1. **本地 → 本地**: 本地文件复制（支持压缩选项）
2. **本地 → 远程**: 上传文件到远程服务器
3. **远程 → 本地**: 从远程服务器下载文件
4. **远程 → 远程**: 服务器间直接传输（通过本地中转）

### 7. 服务器管理

点击"服务器管理"按钮可以：

1. **添加服务器**: 配置新的SSH服务器连接
2. **编辑服务器**: 修改现有服务器配置
3. **删除服务器**: 移除不需要的服务器配置
4. **测试连接**: 验证服务器连接是否正常

### 8. 监控和日志

- 实时进度条显示传输进度
- 详细操作日志记录所有操作
- 传输分析提供优化建议
- 支持随时取消传输操作

## 文件说明

### 程序文件
- `main.py`: 主程序入口
- `src/`: 源代码目录
  - `gui.py`: 图形界面模块
  - `ssh_client.py`: SSH客户端模块
  - `config_manager.py`: 配置管理模块
  - `backup_manager.py`: 备份管理模块
- `requirements.txt`: Python依赖包列表
- `build.py`: 打包脚本
- `test_modules.py`: 模块测试脚本

### 配置文件（运行时生成）
- `servers.json`: 服务器配置文件（加密存储）
- `key.key`: 加密密钥文件
- `backup_history.json`: 备份历史记录

## 安全说明

- 服务器密码使用AES加密存储
- 加密密钥存储在本地`key.key`文件中
- 建议定期备份配置文件
- 不要将密钥文件分享给他人

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证服务器地址和端口
   - 确认用户名和密码正确
   - 检查SSH服务是否启用

2. **传输中断**
   - 检查网络稳定性
   - 确认磁盘空间充足
   - 检查文件权限

3. **程序无法启动**
   - 确认Python版本兼容
   - 检查依赖包是否正确安装
   - 运行`python test_modules.py`检查模块

### 日志查看

程序运行时的详细日志会显示在界面的"操作日志"区域，包括：
- 连接状态
- 传输进度
- 错误信息
- 操作结果

## 技术架构

- **GUI框架**: Tkinter
- **SSH客户端**: Paramiko
- **加密**: Cryptography
- **打包工具**: PyInstaller

## 开发和贡献

### 开发环境设置

1. 安装开发依赖
2. 运行测试：`python test_modules.py`
3. 代码格式化和检查

### 项目结构
```
directory-transfer/
├── src/                    # 源代码
│   ├── __init__.py
│   ├── gui.py             # 图形界面
│   ├── ssh_client.py      # SSH客户端
│   ├── config_manager.py  # 配置管理
│   └── backup_manager.py  # 备份管理
├── main.py                # 主程序
├── requirements.txt       # 依赖列表
├── build.py              # 打包脚本
├── build.bat             # Windows打包批处理
├── test_modules.py       # 测试脚本
└── README.md             # 说明文档
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 技术支持

---

**注意**: 请确保遵守相关法律法规，仅在授权的服务器上使用此工具。
