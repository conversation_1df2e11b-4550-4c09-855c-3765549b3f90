"""
备份面板
管理备份任务的创建、编辑和执行
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import List, Dict, Optional, Callable

try:
    from ..backup_manager import BackupManager, BackupTask
    from ..config_manager import ConfigManager
    from .endpoint_panel import RemoteDirectoryBrowser
    from ..endpoints.remote_endpoint import RemoteEndpoint
except ImportError:
    from backup_manager import BackupManager, BackupTask
    from config_manager import ConfigManager
    from gui_v2.endpoint_panel import RemoteDirectoryBrowser
    from endpoints.remote_endpoint import RemoteEndpoint


class BackupTaskDialog:
    """备份任务编辑对话框"""
    
    def __init__(self, parent, config_manager: ConfigManager, task: BackupTask = None):
        self.parent = parent
        self.config_manager = config_manager
        self.task = task
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("备份任务设置" if task is None else "编辑备份任务")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.create_widgets()
        self.load_task_data()
        
        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # 任务名称
        ttk.Label(main_frame, text="任务名称:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.name_var, width=40).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        row += 1
        
        # 服务器选择
        ttk.Label(main_frame, text="服务器:").grid(row=row, column=0, sticky=tk.W, pady=5)
        server_frame = ttk.Frame(main_frame)
        server_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        server_frame.columnconfigure(0, weight=1)
        
        self.server_var = tk.StringVar()
        self.server_combo = ttk.Combobox(server_frame, textvariable=self.server_var, state="readonly")
        self.server_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(server_frame, text="测试连接", command=self.test_connection).grid(row=0, column=1)
        row += 1
        
        # 源目录列表
        ttk.Label(main_frame, text="备份目录:").grid(row=row, column=0, sticky=(tk.W, tk.N), pady=5)
        
        dir_frame = ttk.Frame(main_frame)
        dir_frame.grid(row=row, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        dir_frame.columnconfigure(0, weight=1)
        dir_frame.rowconfigure(0, weight=1)
        
        # 目录列表
        self.dir_listbox = tk.Listbox(dir_frame, height=6)
        self.dir_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        dir_scrollbar = ttk.Scrollbar(dir_frame, orient=tk.VERTICAL, command=self.dir_listbox.yview)
        dir_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.dir_listbox.config(yscrollcommand=dir_scrollbar.set)
        
        # 目录操作按钮
        dir_buttons = ttk.Frame(dir_frame)
        dir_buttons.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(dir_buttons, text="添加目录", command=self.add_directory).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(dir_buttons, text="删除选中", command=self.remove_directory).pack(side=tk.LEFT)
        
        main_frame.rowconfigure(row, weight=1)
        row += 1
        
        # 本地备份根目录
        ttk.Label(main_frame, text="本地备份目录:").grid(row=row, column=0, sticky=tk.W, pady=5)
        backup_frame = ttk.Frame(main_frame)
        backup_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        backup_frame.columnconfigure(0, weight=1)
        
        self.backup_root_var = tk.StringVar()
        ttk.Entry(backup_frame, textvariable=self.backup_root_var).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(backup_frame, text="浏览", command=self.browse_backup_root).grid(row=0, column=1)
        row += 1
        
        # 选项
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding="5")
        options_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        self.compression_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="使用压缩传输", variable=self.compression_var).pack(anchor=tk.W)
        
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="启用任务", variable=self.enabled_var).pack(anchor=tk.W)
        row += 1
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT)
        
        # 加载服务器列表
        self.refresh_server_list()
    
    def refresh_server_list(self):
        """刷新服务器列表"""
        servers = self.config_manager.list_servers()
        self.server_combo['values'] = servers
        if servers and not self.server_var.get():
            self.server_var.set(servers[0])
    
    def test_connection(self):
        """测试服务器连接"""
        server_name = self.server_var.get()
        if not server_name:
            messagebox.showwarning("警告", "请先选择服务器")
            return
        
        try:
            remote_endpoint = RemoteEndpoint(server_name, self.config_manager)
            success, message = remote_endpoint.connect()
            if success:
                messagebox.showinfo("成功", "连接测试成功")
                remote_endpoint.disconnect()
            else:
                messagebox.showerror("失败", f"连接测试失败: {message}")
        except Exception as e:
            messagebox.showerror("错误", f"连接测试出错: {str(e)}")
    
    def add_directory(self):
        """添加备份目录"""
        server_name = self.server_var.get()
        if not server_name:
            messagebox.showwarning("警告", "请先选择服务器")
            return
        
        try:
            remote_endpoint = RemoteEndpoint(server_name, self.config_manager)
            success, message = remote_endpoint.connect()
            if not success:
                messagebox.showerror("错误", f"连接服务器失败: {message}")
                return
            
            # 打开远程目录浏览器
            dialog = RemoteDirectoryBrowser(self.dialog, remote_endpoint, "/")
            if dialog.result:
                # 检查是否已存在
                if dialog.result not in [self.dir_listbox.get(i) for i in range(self.dir_listbox.size())]:
                    self.dir_listbox.insert(tk.END, dialog.result)
                else:
                    messagebox.showinfo("提示", "该目录已在备份列表中")
            
            remote_endpoint.disconnect()
            
        except Exception as e:
            messagebox.showerror("错误", f"添加目录失败: {str(e)}")
    
    def remove_directory(self):
        """删除选中的目录"""
        selection = self.dir_listbox.curselection()
        if selection:
            self.dir_listbox.delete(selection[0])
        else:
            messagebox.showwarning("警告", "请先选择要删除的目录")
    
    def browse_backup_root(self):
        """浏览本地备份根目录"""
        directory = filedialog.askdirectory(title="选择本地备份根目录")
        if directory:
            self.backup_root_var.set(directory)
    
    def load_task_data(self):
        """加载任务数据"""
        if self.task:
            self.name_var.set(self.task.name)
            self.server_var.set(self.task.server_name)
            self.backup_root_var.set(self.task.local_backup_root)
            self.compression_var.set(self.task.use_compression)
            self.enabled_var.set(self.task.enabled)
            
            # 加载源目录列表
            for directory in self.task.source_directories:
                self.dir_listbox.insert(tk.END, directory)
    
    def on_ok(self):
        """确定按钮处理"""
        # 验证输入
        if not self.name_var.get().strip():
            messagebox.showerror("错误", "请输入任务名称")
            return
        
        if not self.server_var.get():
            messagebox.showerror("错误", "请选择服务器")
            return
        
        if self.dir_listbox.size() == 0:
            messagebox.showerror("错误", "请至少添加一个备份目录")
            return
        
        if not self.backup_root_var.get().strip():
            messagebox.showerror("错误", "请选择本地备份根目录")
            return
        
        # 创建或更新任务
        if self.task is None:
            self.task = BackupTask()
        
        self.task.name = self.name_var.get().strip()
        self.task.server_name = self.server_var.get()
        self.task.source_directories = [self.dir_listbox.get(i) for i in range(self.dir_listbox.size())]
        self.task.local_backup_root = self.backup_root_var.get().strip()
        self.task.use_compression = self.compression_var.get()
        self.task.enabled = self.enabled_var.get()
        
        self.result = self.task
        self.dialog.destroy()
    
    def on_cancel(self):
        """取消按钮处理"""
        self.result = None
        self.dialog.destroy()


class BackupPanel:
    """备份面板"""
    
    def __init__(self, parent, config_manager: ConfigManager):
        self.parent = parent
        self.config_manager = config_manager
        self.backup_manager = BackupManager(config_manager)
        
        # 创建主框架
        self.frame = ttk.LabelFrame(parent, text="服务器备份", padding="10")
        
        self.create_widgets()
        self.refresh_task_list()
    
    def create_widgets(self):
        """创建界面组件"""
        # 任务列表
        list_frame = ttk.Frame(self.frame)
        list_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview显示任务列表
        columns = ('name', 'server', 'directories', 'last_run', 'status')
        self.task_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        self.task_tree.heading('name', text='任务名称')
        self.task_tree.heading('server', text='服务器')
        self.task_tree.heading('directories', text='备份目录数')
        self.task_tree.heading('last_run', text='最后运行')
        self.task_tree.heading('status', text='状态')
        
        # 设置列宽
        self.task_tree.column('name', width=150)
        self.task_tree.column('server', width=100)
        self.task_tree.column('directories', width=80)
        self.task_tree.column('last_run', width=120)
        self.task_tree.column('status', width=200)
        
        self.task_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        tree_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        tree_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.task_tree.config(yscrollcommand=tree_scrollbar.set)
        
        # 按钮区域
        button_frame = ttk.Frame(self.frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="新建任务", command=self.create_task).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="编辑任务", command=self.edit_task).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除任务", command=self.delete_task).pack(side=tk.LEFT, padx=(0, 5))
        self.execute_button = ttk.Button(button_frame, text="执行备份", command=self.execute_backup)
        self.execute_button.pack(side=tk.LEFT, padx=(0, 5))
        self.cancel_button = ttk.Button(button_frame, text="取消备份", command=self.cancel_backup, state="disabled")
        self.cancel_button.pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新", command=self.refresh_task_list).pack(side=tk.LEFT)

        # 进度显示区域
        progress_frame = ttk.LabelFrame(self.frame, text="备份进度", padding="5")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        progress_frame.columnconfigure(0, weight=1)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # 进度标签
        self.progress_label = ttk.Label(progress_frame, text="就绪")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)

        # 备份状态变量
        self.backup_in_progress = False
        self.backup_thread = None

        # 配置网格权重
        self.frame.columnconfigure(0, weight=1)
        self.frame.rowconfigure(0, weight=1)
    
    def refresh_task_list(self):
        """刷新任务列表"""
        # 清空现有项目
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        
        # 添加任务
        tasks = self.backup_manager.get_backup_tasks()
        for task in tasks:
            last_run = task.last_run_time
            if last_run:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_run)
                    last_run = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    pass
            else:
                last_run = "从未运行"
            
            status = task.last_run_status or ("启用" if task.enabled else "禁用")
            
            self.task_tree.insert('', tk.END, values=(
                task.name,
                task.server_name,
                len(task.source_directories),
                last_run,
                status
            ), tags=(task.task_id,))
    
    def create_task(self):
        """创建新任务"""
        dialog = BackupTaskDialog(self.parent, self.config_manager)
        self.parent.wait_window(dialog.dialog)
        
        if dialog.result:
            self.backup_manager.create_backup_task(
                dialog.result.name,
                dialog.result.server_name,
                dialog.result.source_directories,
                dialog.result.local_backup_root,
                dialog.result.use_compression
            )
            self.refresh_task_list()
            messagebox.showinfo("成功", "备份任务创建成功")
    
    def edit_task(self):
        """编辑选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的任务")
            return
        
        # 获取任务ID
        item = selection[0]
        task_id = self.task_tree.item(item)['tags'][0]
        task = self.backup_manager.get_backup_task(task_id)
        
        if not task:
            messagebox.showerror("错误", "任务不存在")
            return
        
        dialog = BackupTaskDialog(self.parent, self.config_manager, task)
        self.parent.wait_window(dialog.dialog)
        
        if dialog.result:
            self.backup_manager.update_backup_task(dialog.result)
            self.refresh_task_list()
            messagebox.showinfo("成功", "备份任务更新成功")
    
    def delete_task(self):
        """删除选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的任务")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的备份任务吗？"):
            item = selection[0]
            task_id = self.task_tree.item(item)['tags'][0]
            
            if self.backup_manager.delete_backup_task(task_id):
                self.refresh_task_list()
                messagebox.showinfo("成功", "备份任务删除成功")
            else:
                messagebox.showerror("错误", "删除备份任务失败")
    
    def execute_backup(self):
        """执行选中的备份任务"""
        if self.backup_in_progress:
            messagebox.showwarning("警告", "备份正在进行中，请等待完成")
            return

        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要执行的任务")
            return

        item = selection[0]
        task_id = self.task_tree.item(item)['tags'][0]
        task = self.backup_manager.get_backup_task(task_id)

        if not task:
            messagebox.showerror("错误", "任务不存在")
            return

        if not task.enabled:
            messagebox.showwarning("警告", "任务已禁用，无法执行")
            return

        # 设置备份状态
        self.backup_in_progress = True
        self.execute_button.config(state="disabled")
        self.cancel_button.config(state="normal")
        self.progress_var.set(0)
        self.progress_label.config(text="准备开始备份...")

        # 在新线程中执行备份
        def backup_worker():
            try:
                def progress_callback(current, total, message):
                    # 更新进度条和标签
                    self.parent.after(0, lambda: self.update_progress(current, message))

                success, message = self.backup_manager.execute_backup_task(task_id, progress_callback)

                # 在主线程中更新UI
                self.parent.after(0, lambda: self.on_backup_complete(success, message))

            except Exception as e:
                self.parent.after(0, lambda: self.on_backup_complete(False, f"备份执行异常: {str(e)}"))

        # 启动备份线程
        self.backup_thread = threading.Thread(target=backup_worker, daemon=True)
        self.backup_thread.start()

        # 显示开始信息
        self.progress_label.config(text=f"开始执行备份任务: {task.name}")

    def cancel_backup(self):
        """取消备份"""
        if self.backup_in_progress:
            if messagebox.askyesno("确认", "确定要取消当前备份吗？"):
                # 这里可以添加取消备份的逻辑
                self.backup_in_progress = False
                self.execute_button.config(state="normal")
                self.cancel_button.config(state="disabled")
                self.progress_var.set(0)
                self.progress_label.config(text="备份已取消")

    def update_progress(self, progress: float, message: str):
        """更新进度显示"""
        self.progress_var.set(progress)
        self.progress_label.config(text=message)
    
    def on_backup_complete(self, success: bool, message: str):
        """备份完成回调"""
        # 重置备份状态
        self.backup_in_progress = False
        self.execute_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        self.backup_thread = None

        # 更新进度显示
        if success:
            self.progress_var.set(100)
            self.progress_label.config(text=f"备份完成: {message}")
            messagebox.showinfo("成功", f"备份完成: {message}")
        else:
            self.progress_label.config(text=f"备份失败: {message}")
            messagebox.showerror("失败", f"备份失败: {message}")

        # 刷新任务列表
        self.refresh_task_list()
