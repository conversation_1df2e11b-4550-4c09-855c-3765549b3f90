"""
传输控制面板
配置传输选项和设置
"""
import tkinter as tk
from tkinter import ttk


class TransferPanel:
    """传输控制面板"""
    
    def __init__(self, parent, change_callback=None):
        self.change_callback = change_callback
        
        # 创建主框架
        self.frame = ttk.LabelFrame(parent, text="传输选项", padding="10")
        
        # 界面变量
        self.use_compression = tk.BooleanVar(value=True)
        self.verify_transfer = tk.BooleanVar(value=False)
        self.compression_level = tk.IntVar(value=6)
        
        self.create_widgets()
        self.bind_events()
    
    def create_widgets(self):
        """创建界面组件"""
        # 第一行：压缩选项
        compression_frame = ttk.Frame(self.frame)
        compression_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.compression_check = ttk.Checkbutton(
            compression_frame, 
            text="启用压缩 (tar.gz)", 
            variable=self.use_compression,
            command=self.on_compression_changed
        )
        self.compression_check.pack(side=tk.LEFT)
        
        # 压缩级别
        self.compression_level_frame = ttk.Frame(compression_frame)
        self.compression_level_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        ttk.Label(self.compression_level_frame, text="压缩级别:").pack(side=tk.LEFT)
        self.compression_scale = ttk.Scale(
            self.compression_level_frame,
            from_=1, to=9,
            variable=self.compression_level,
            orient=tk.HORIZONTAL,
            length=100,
            command=self.on_compression_level_changed
        )
        self.compression_scale.pack(side=tk.LEFT, padx=(5, 5))
        
        self.compression_level_label = ttk.Label(self.compression_level_frame, text="6")
        self.compression_level_label.pack(side=tk.LEFT)
        
        # 第二行：其他选项
        options_frame = ttk.Frame(self.frame)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(
            options_frame, 
            text="传输后验证", 
            variable=self.verify_transfer,
            command=self.on_verify_changed
        ).pack(side=tk.LEFT)
        
        # 第三行：传输信息和建议
        info_frame = ttk.Frame(self.frame)
        info_frame.pack(fill=tk.X)
        
        self.info_label = ttk.Label(
            info_frame, 
            text="💡 建议: 网络传输时启用压缩可显著减少传输时间", 
            foreground="blue",
            font=("Arial", 9)
        )
        self.info_label.pack(fill=tk.X)
        
        # 初始状态
        self.on_compression_changed()
    
    def bind_events(self):
        """绑定事件"""
        pass
    
    def on_compression_changed(self):
        """压缩选项变化"""
        use_compression = self.use_compression.get()
        
        # 启用/禁用压缩级别控件
        state = "normal" if use_compression else "disabled"
        self.compression_scale.config(state=state)
        
        # 更新信息文本
        if use_compression:
            level = self.compression_level.get()
            if level <= 3:
                info_text = "💡 低压缩级别: 速度快，压缩率低"
            elif level <= 6:
                info_text = "💡 中等压缩级别: 平衡速度和压缩率"
            else:
                info_text = "💡 高压缩级别: 速度慢，压缩率高"
        else:
            info_text = "💡 未启用压缩: 适合已压缩文件或本地传输"
        
        self.info_label.config(text=info_text)
        
        self.notify_change()
    
    def on_compression_level_changed(self, value=None):
        """压缩级别变化"""
        level = int(float(value)) if value else self.compression_level.get()
        self.compression_level.set(level)
        self.compression_level_label.config(text=str(level))
        
        # 更新信息
        self.on_compression_changed()
    
    def on_verify_changed(self):
        """验证选项变化"""
        self.notify_change()
    
    def get_settings(self):
        """获取传输设置"""
        return {
            'use_compression': self.use_compression.get(),
            'compression_level': self.compression_level.get(),
            'verify_transfer': self.verify_transfer.get()
        }
    
    def set_settings(self, settings):
        """设置传输选项"""
        if 'use_compression' in settings:
            self.use_compression.set(settings['use_compression'])
        
        if 'compression_level' in settings:
            self.compression_level.set(settings['compression_level'])
            self.compression_level_label.config(text=str(settings['compression_level']))
        
        if 'verify_transfer' in settings:
            self.verify_transfer.set(settings['verify_transfer'])
        
        self.on_compression_changed()
    
    def get_recommendations(self, transfer_mode=None, file_size=0):
        """获取传输建议"""
        recommendations = []
        
        # 根据传输模式给出建议
        if transfer_mode:
            if "remote" in transfer_mode.lower():
                if not self.use_compression.get() and file_size > 10 * 1024 * 1024:  # 10MB
                    recommendations.append("建议启用压缩以减少网络传输时间")
            elif transfer_mode.lower() == "local_to_local":
                if self.use_compression.get() and file_size < 50 * 1024 * 1024:  # 50MB
                    recommendations.append("本地传输可考虑禁用压缩以提高速度")
        
        # 根据压缩级别给出建议
        if self.use_compression.get():
            level = self.compression_level.get()
            if level > 7 and file_size > 1024 * 1024 * 1024:  # 1GB
                recommendations.append("大文件建议使用中等压缩级别以平衡速度")
        
        return recommendations
    
    def update_recommendations(self, transfer_mode=None, file_size=0):
        """更新建议显示"""
        recommendations = self.get_recommendations(transfer_mode, file_size)
        
        if recommendations:
            self.info_label.config(text=f"💡 {recommendations[0]}")
        else:
            self.on_compression_changed()  # 显示默认信息
    
    def notify_change(self):
        """通知设置变化"""
        if self.change_callback:
            self.change_callback(self.get_settings())
