"""
应用常量定义
包含应用程序中使用的所有常量
"""

# 应用信息
APP_NAME = "远程文件备份工具"
APP_VERSION = "2.0"
APP_TITLE = f"{APP_NAME} V{APP_VERSION}"

# 窗口设置
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
WINDOW_MIN_WIDTH = 1000
WINDOW_MIN_HEIGHT = 600

# 文件路径
DATA_DIR = "data"
CONFIG_DIR = "config"
DOCS_DIR = "docs"
LOGS_DIR = "logs"

# 数据文件
SERVERS_FILE = f"{DATA_DIR}/servers.json"
KEY_FILE = f"{DATA_DIR}/key.key"
BACKUP_TASKS_FILE = f"{DATA_DIR}/backup_tasks.json"
BACKUP_HISTORY_FILE = f"{DATA_DIR}/backup_history.json"

# 网络设置
DEFAULT_SSH_PORT = 22
CONNECTION_TIMEOUT = 30
TRANSFER_TIMEOUT = 600

# 路径限制
WINDOWS_MAX_PATH_LENGTH = 260
SAFE_PATH_LENGTH = 240
MAX_FILENAME_LENGTH = 255
SAFE_FILENAME_LENGTH = 200

# 压缩设置
DEFAULT_COMPRESSION_LEVEL = 6
SUPPORTED_COMPRESSION_FORMATS = ['.tar.gz', '.zip']

# GUI设置
PROGRESS_UPDATE_INTERVAL = 100  # 毫秒
LOG_MAX_LINES = 1000

# 备份设置
BACKUP_TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
MAX_BACKUP_HISTORY = 100

# 错误代码
ERROR_CODES = {
    'CONNECTION_FAILED': 1001,
    'AUTHENTICATION_FAILED': 1002,
    'PATH_NOT_FOUND': 1003,
    'PERMISSION_DENIED': 1004,
    'TRANSFER_FAILED': 1005,
    'COMPRESSION_FAILED': 1006,
    'PATH_TOO_LONG': 1007,
}

# 状态码
STATUS_CODES = {
    'SUCCESS': 0,
    'WARNING': 1,
    'ERROR': 2,
    'CANCELLED': 3,
}

# 日志级别
LOG_LEVELS = {
    'DEBUG': 10,
    'INFO': 20,
    'WARNING': 30,
    'ERROR': 40,
    'CRITICAL': 50,
}

# 支持的文件类型
SUPPORTED_FILE_TYPES = [
    '.txt', '.log', '.json', '.xml', '.csv',
    '.py', '.js', '.html', '.css', '.sql',
    '.jpg', '.png', '.gif', '.pdf', '.doc',
    '.zip', '.tar', '.gz', '.rar', '.7z'
]

# 默认配置
DEFAULT_CONFIG = {
    'auto_save': True,
    'show_hidden_files': False,
    'compression_enabled': True,
    'compression_level': DEFAULT_COMPRESSION_LEVEL,
    'max_concurrent_transfers': 3,
    'retry_attempts': 3,
    'retry_delay': 5,  # 秒
}

# 主题设置
THEMES = {
    'light': {
        'bg_color': '#ffffff',
        'fg_color': '#000000',
        'select_color': '#0078d4',
    },
    'dark': {
        'bg_color': '#2d2d2d',
        'fg_color': '#ffffff',
        'select_color': '#0078d4',
    }
}

# 快捷键
SHORTCUTS = {
    'new_task': 'Ctrl+N',
    'save': 'Ctrl+S',
    'refresh': 'F5',
    'exit': 'Ctrl+Q',
    'help': 'F1',
}
