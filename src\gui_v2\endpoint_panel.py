"""
端点配置面板
支持本地和远程端点的配置
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os

try:
    from ..endpoints.remote_endpoint import RemoteEndpoint
except ImportError:
    from endpoints.remote_endpoint import RemoteEndpoint


class EndpointPanel:
    """端点配置面板"""
    
    def __init__(self, parent, title, config_manager, change_callback=None):
        self.config_manager = config_manager
        self.change_callback = change_callback
        
        # 创建主框架
        self.frame = ttk.LabelFrame(parent, text=title, padding="10")
        
        # 界面变量
        self.endpoint_type = tk.StringVar(value="local")
        self.selected_server = tk.StringVar()
        self.path = tk.StringVar()
        
        # 当前连接的远程端点
        self.current_remote_endpoint = None
        
        self.create_widgets()
        self.bind_events()
    
    def create_widgets(self):
        """创建界面组件"""
        # 端点类型选择
        type_frame = ttk.Frame(self.frame)
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(type_frame, text="端点类型:").pack(side=tk.LEFT)
        
        ttk.Radiobutton(type_frame, text="本地", variable=self.endpoint_type, 
                       value="local", command=self.on_type_changed).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(type_frame, text="远程服务器", variable=self.endpoint_type, 
                       value="remote", command=self.on_type_changed).pack(side=tk.LEFT, padx=(10, 0))
        
        # 服务器选择（仅远程时显示）
        self.server_frame = ttk.Frame(self.frame)
        self.server_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(self.server_frame, text="服务器:").pack(side=tk.LEFT)
        self.server_combo = ttk.Combobox(self.server_frame, textvariable=self.selected_server, 
                                        state="readonly", width=20)
        self.server_combo.pack(side=tk.LEFT, padx=(10, 5))
        
        self.connect_button = ttk.Button(self.server_frame, text="连接", command=self.connect_server)
        self.connect_button.pack(side=tk.LEFT, padx=(5, 0))
        
        # 连接状态
        self.status_label = ttk.Label(self.server_frame, text="", foreground="gray")
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 路径配置
        path_frame = ttk.Frame(self.frame)
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(path_frame, text="路径:").pack(side=tk.LEFT)
        self.path_entry = ttk.Entry(path_frame, textvariable=self.path, width=30)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))
        
        self.browse_button = ttk.Button(path_frame, text="浏览", command=self.browse_path)
        self.browse_button.pack(side=tk.LEFT)
        
        # 路径信息显示
        self.info_frame = ttk.Frame(self.frame)
        self.info_frame.pack(fill=tk.X)
        
        self.info_label = ttk.Label(self.info_frame, text="", foreground="blue", font=("Arial", 8))
        self.info_label.pack(fill=tk.X)
        
        # 初始状态
        self.on_type_changed()
    
    def bind_events(self):
        """绑定事件"""
        self.server_combo.bind('<<ComboboxSelected>>', self.on_server_changed)
        self.path_entry.bind('<KeyRelease>', self.on_path_changed)
        self.path_entry.bind('<FocusOut>', self.on_path_changed)
    
    def on_type_changed(self):
        """端点类型变化"""
        endpoint_type = self.endpoint_type.get()
        
        if endpoint_type == "local":
            # 隐藏服务器选择
            self.server_frame.pack_forget()
            # 设置默认本地路径
            if not self.path.get():
                self.path.set(os.path.expanduser("~"))
        else:
            # 显示服务器选择
            self.server_frame.pack(fill=tk.X, pady=(0, 10))
            # 清空路径
            if self.path.get() == os.path.expanduser("~"):
                self.path.set("")
        
        self.update_info()
        self.notify_change()
    
    def on_server_changed(self, event=None):
        """服务器选择变化"""
        self.status_label.config(text="未连接", foreground="gray")
        self.current_remote_endpoint = None
        self.update_info()
        self.notify_change()
    
    def on_path_changed(self, event=None):
        """路径变化"""
        self.update_info()
        self.notify_change()
    
    def connect_server(self):
        """连接服务器"""
        server_name = self.selected_server.get()
        if not server_name:
            messagebox.showwarning("警告", "请选择服务器")
            return
        
        try:
            self.status_label.config(text="连接中...", foreground="orange")
            self.connect_button.config(state="disabled")
            
            # 创建远程端点
            remote_endpoint = RemoteEndpoint(server_name, self.config_manager)
            success, message = remote_endpoint.connect()
            
            if success:
                self.current_remote_endpoint = remote_endpoint
                self.status_label.config(text="已连接", foreground="green")
                
                # 设置默认远程路径
                if not self.path.get():
                    home_dir = remote_endpoint.get_home_directory()
                    self.path.set(home_dir)
                
                self.update_info()
                self.notify_change()
            else:
                self.status_label.config(text="连接失败", foreground="red")
                messagebox.showerror("错误", f"连接失败: {message}")
            
        except Exception as e:
            self.status_label.config(text="连接异常", foreground="red")
            messagebox.showerror("错误", f"连接异常: {str(e)}")
        finally:
            self.connect_button.config(state="normal")
    
    def browse_path(self):
        """浏览路径"""
        endpoint_type = self.endpoint_type.get()
        
        if endpoint_type == "local":
            # 本地路径浏览
            path = filedialog.askdirectory(title="选择本地目录", initialdir=self.path.get())
            if path:
                self.path.set(path)
                self.update_info()
                self.notify_change()
        else:
            # 远程路径浏览
            if not self.current_remote_endpoint or not self.current_remote_endpoint.is_connected():
                messagebox.showwarning("警告", "请先连接到服务器")
                return
            
            try:
                current_path = self.path.get() or "/"
                dialog = RemoteDirectoryBrowser(
                    self.frame.winfo_toplevel(),
                    self.current_remote_endpoint,
                    current_path
                )
                if dialog.result:
                    self.path.set(dialog.result)
                    self.update_info()
                    self.notify_change()
            except Exception as e:
                messagebox.showerror("错误", f"浏览远程目录失败: {str(e)}")
    
    def update_info(self):
        """更新路径信息显示"""
        endpoint_type = self.endpoint_type.get()
        path = self.path.get()
        
        if not path:
            self.info_label.config(text="")
            return
        
        try:
            if endpoint_type == "local":
                if os.path.exists(path):
                    if os.path.isdir(path):
                        # 统计目录信息
                        try:
                            items = os.listdir(path)
                            dirs = sum(1 for item in items if os.path.isdir(os.path.join(path, item)))
                            files = len(items) - dirs
                            self.info_label.config(text=f"目录: {dirs} 个子目录, {files} 个文件")
                        except PermissionError:
                            self.info_label.config(text="目录: 无访问权限")
                    else:
                        size = os.path.getsize(path)
                        self.info_label.config(text=f"文件: {self.format_size(size)}")
                else:
                    self.info_label.config(text="路径不存在")
            else:
                if self.current_remote_endpoint and self.current_remote_endpoint.is_connected():
                    if self.current_remote_endpoint.path_exists(path):
                        if self.current_remote_endpoint.is_directory(path):
                            # 获取远程目录信息
                            success, files = self.current_remote_endpoint.list_directory(path)
                            if success:
                                dirs = sum(1 for f in files if f.get('is_dir', False))
                                file_count = len(files) - dirs
                                self.info_label.config(text=f"远程目录: {dirs} 个子目录, {file_count} 个文件")
                            else:
                                self.info_label.config(text="远程目录: 无法访问")
                        else:
                            path_info = self.current_remote_endpoint.get_path_info(path)
                            size = path_info.get('size', 0)
                            self.info_label.config(text=f"远程文件: {self.format_size(size)}")
                    else:
                        self.info_label.config(text="远程路径不存在")
                else:
                    self.info_label.config(text="未连接到服务器")
        except Exception as e:
            self.info_label.config(text=f"获取信息失败: {str(e)}")
    
    def format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def update_server_list(self, servers):
        """更新服务器列表"""
        self.server_combo['values'] = servers
        if servers and not self.selected_server.get():
            self.selected_server.set(servers[0])
    
    def get_endpoint_info(self):
        """获取端点信息"""
        endpoint_type = self.endpoint_type.get()
        path = self.path.get()
        
        if not path:
            return None
        
        info = {
            'type': endpoint_type,
            'path': path
        }
        
        if endpoint_type == "remote":
            server_name = self.selected_server.get()
            if not server_name:
                return None
            info['server_name'] = server_name
            
            if not self.current_remote_endpoint or not self.current_remote_endpoint.is_connected():
                return None
        
        return info
    
    def notify_change(self):
        """通知变化"""
        if self.change_callback:
            endpoint_info = self.get_endpoint_info()
            if endpoint_info:
                self.change_callback(
                    endpoint_info['type'],
                    endpoint_info.get('server_name'),
                    endpoint_info['path']
                )


class RemoteDirectoryBrowser:
    """简化的远程目录浏览器"""

    def __init__(self, parent, remote_endpoint, initial_path="/"):
        self.result = None
        self.remote_endpoint = remote_endpoint
        self.current_path = initial_path

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("浏览远程目录")
        self.dialog.geometry("500x400")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_widgets()
        self.load_directory(self.current_path)

        # 等待对话框关闭
        self.dialog.wait_window()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 路径显示和导航
        path_frame = ttk.Frame(main_frame)
        path_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(path_frame, text="当前路径:").pack(side=tk.LEFT)
        self.path_var = tk.StringVar(value=self.current_path)
        self.path_entry = ttk.Entry(path_frame, textvariable=self.path_var, width=40)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        ttk.Button(path_frame, text="转到", command=self.go_to_path).pack(side=tk.LEFT)
        ttk.Button(path_frame, text="上级", command=self.go_up).pack(side=tk.LEFT, padx=(5, 0))

        # 文件列表
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建Listbox
        self.listbox = tk.Listbox(list_frame)
        self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.listbox.configure(yscrollcommand=scrollbar.set)

        # 绑定双击事件
        self.listbox.bind('<Double-1>', self.on_item_double_click)

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(fill=tk.X, pady=(0, 10))

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="选择此目录", command=self.select_current_dir).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="刷新", command=self.refresh).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT)

    def load_directory(self, path):
        """加载目录内容"""
        try:
            self.status_var.set("正在加载目录...")
            self.dialog.update_idletasks()

            # 清空现有内容
            self.listbox.delete(0, tk.END)

            # 获取目录内容
            success, files = self.remote_endpoint.list_directory(path)

            if not success:
                self.status_var.set(f"加载失败: {files}")
                return

            # 排序：目录在前，文件在后
            files.sort(key=lambda x: (not x['is_dir'], x['name'].lower()))

            # 添加到列表
            for file_info in files:
                name = file_info['name']
                is_dir = file_info['is_dir']

                if is_dir:
                    display_name = f"📁 {name}/"
                else:
                    size = file_info.get('size', 0)
                    size_str = self.format_size(size)
                    display_name = f"📄 {name} ({size_str})"

                self.listbox.insert(tk.END, display_name)

            self.current_path = path
            self.path_var.set(path)
            self.status_var.set(f"已加载 {len(files)} 个项目")

        except Exception as e:
            self.status_var.set(f"加载目录失败: {str(e)}")

    def format_size(self, size):
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    def on_item_double_click(self, event):
        """处理项目双击事件"""
        selection = self.listbox.curselection()
        if not selection:
            return

        item_text = self.listbox.get(selection[0])

        if item_text.startswith("📁"):
            # 进入目录
            name = item_text[2:].rstrip('/')  # 移除图标和末尾的斜杠
            new_path = self.current_path.rstrip('/') + '/' + name
            if self.current_path == '/':
                new_path = '/' + name
            self.load_directory(new_path)

    def go_to_path(self):
        """转到指定路径"""
        path = self.path_var.get().strip()
        if path:
            self.load_directory(path)

    def go_up(self):
        """返回上级目录"""
        if self.current_path == '/':
            return

        parent_path = '/'.join(self.current_path.rstrip('/').split('/')[:-1])
        if not parent_path:
            parent_path = '/'

        self.load_directory(parent_path)

    def refresh(self):
        """刷新当前目录"""
        self.load_directory(self.current_path)

    def select_current_dir(self):
        """选择当前目录"""
        self.result = self.current_path
        self.dialog.destroy()

    def cancel(self):
        """取消选择"""
        self.dialog.destroy()
