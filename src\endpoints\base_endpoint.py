"""
端点基类
定义传输端点的通用接口
"""
import os
from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Optional, Callable


class PathUtils:
    """路径处理工具类"""

    @staticmethod
    def normalize_local_path(path: str) -> str:
        """标准化本地路径（Windows使用反斜杠）"""
        if not path:
            return ""
        # 展开用户目录
        path = os.path.expanduser(path)
        # 标准化路径
        path = os.path.normpath(path)
        return path

    @staticmethod
    def normalize_remote_path(path: str) -> str:
        """标准化远程路径（Linux使用正斜杠）"""
        if not path:
            return "/"

        # 确保以/开头
        if not path.startswith('/'):
            path = '/' + path

        # 移除重复的斜杠
        while '//' in path:
            path = path.replace('//', '/')

        # 移除末尾的斜杠（除非是根目录）
        if len(path) > 1 and path.endswith('/'):
            path = path.rstrip('/')

        return path

    @staticmethod
    def join_local_path(*parts) -> str:
        """连接本地路径"""
        return os.path.join(*[str(part) for part in parts if part])

    @staticmethod
    def join_remote_path(*parts) -> str:
        """连接远程路径"""
        # 过滤空部分并转换为字符串
        clean_parts = [str(part).strip('/') for part in parts if part]
        if not clean_parts:
            return "/"

        # 连接路径
        path = '/' + '/'.join(clean_parts)
        return PathUtils.normalize_remote_path(path)

    @staticmethod
    def get_local_parent(path: str) -> str:
        """获取本地路径的父目录"""
        return os.path.dirname(PathUtils.normalize_local_path(path))

    @staticmethod
    def get_remote_parent(path: str) -> str:
        """获取远程路径的父目录"""
        path = PathUtils.normalize_remote_path(path)
        if path == '/':
            return '/'
        return '/'.join(path.split('/')[:-1]) or '/'

    @staticmethod
    def get_local_basename(path: str) -> str:
        """获取本地路径的基本名称"""
        return os.path.basename(PathUtils.normalize_local_path(path))

    @staticmethod
    def get_remote_basename(path: str) -> str:
        """获取远程路径的基本名称"""
        path = PathUtils.normalize_remote_path(path)
        return path.split('/')[-1] if path != '/' else '/'

    @staticmethod
    def convert_path_for_transfer(path: str, from_type: str, to_type: str) -> str:
        """
        在传输时转换路径格式

        Args:
            path: 原始路径
            from_type: 源端点类型 ('local' 或 'remote')
            to_type: 目标端点类型 ('local' 或 'remote')

        Returns:
            转换后的路径
        """
        if from_type == to_type:
            return path

        if from_type == 'local' and to_type == 'remote':
            # Windows路径转Linux路径
            # 将反斜杠转换为正斜杠
            converted = path.replace('\\', '/')
            return PathUtils.normalize_remote_path(converted)

        elif from_type == 'remote' and to_type == 'local':
            # Linux路径转Windows路径
            # 将正斜杠转换为反斜杠
            converted = path.replace('/', '\\')
            return PathUtils.normalize_local_path(converted)

        return path


class BaseEndpoint(ABC):
    """传输端点基类"""
    
    def __init__(self, endpoint_type: str):
        self.endpoint_type = endpoint_type  # "local" 或 "remote"
        self.connected = False
    
    @abstractmethod
    def connect(self) -> Tuple[bool, str]:
        """
        连接到端点
        
        Returns:
            (成功标志, 消息)
        """
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    def list_directory(self, path: str) -> Tuple[bool, List[Dict]]:
        """
        列出目录内容
        
        Args:
            path: 目录路径
            
        Returns:
            (成功标志, 文件列表)
        """
        pass
    
    @abstractmethod
    def path_exists(self, path: str) -> bool:
        """检查路径是否存在"""
        pass
    
    @abstractmethod
    def is_directory(self, path: str) -> bool:
        """检查是否为目录"""
        pass
    
    @abstractmethod
    def get_path_info(self, path: str) -> Dict:
        """
        获取路径信息
        
        Returns:
            包含大小、修改时间等信息的字典
        """
        pass
    
    @abstractmethod
    def create_directory(self, path: str) -> bool:
        """创建目录"""
        pass
    
    @abstractmethod
    def remove_path(self, path: str) -> bool:
        """删除文件或目录"""
        pass
    
    def get_display_name(self) -> str:
        """获取端点显示名称"""
        return self.endpoint_type.title()
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connected
