"""
传输策略
定义不同传输场景的策略和优化
"""
from abc import ABC, abstractmethod
from typing import Dict, Tuple, Optional, Callable
from enum import Enum


class TransferStrategy(ABC):
    """传输策略基类"""
    
    @abstractmethod
    def estimate_time(self, plan: Dict) -> int:
        """估算传输时间（秒）"""
        pass
    
    @abstractmethod
    def optimize_plan(self, plan: Dict) -> Dict:
        """优化传输计划"""
        pass
    
    @abstractmethod
    def validate_plan(self, plan: Dict) -> Tuple[bool, str]:
        """验证传输计划"""
        pass


class CompressionStrategy:
    """压缩策略"""
    
    @staticmethod
    def should_use_compression(source_size: int, file_types: list = None) -> bool:
        """
        判断是否应该使用压缩
        
        Args:
            source_size: 源文件大小（字节）
            file_types: 文件类型列表
            
        Returns:
            是否建议使用压缩
        """
        # 小于1MB的文件不建议压缩
        if source_size < 1024 * 1024:
            return False
        
        # 已压缩文件类型不建议再压缩
        compressed_extensions = {
            '.zip', '.rar', '.7z', '.gz', '.bz2', '.xz',
            '.jpg', '.jpeg', '.png', '.gif', '.mp3', '.mp4',
            '.avi', '.mkv', '.pdf', '.docx', '.xlsx'
        }
        
        if file_types:
            for file_type in file_types:
                if any(file_type.lower().endswith(ext) for ext in compressed_extensions):
                    return False
        
        # 大于10MB的文件建议压缩
        if source_size > 10 * 1024 * 1024:
            return True
        
        # 默认建议压缩
        return True
    
    @staticmethod
    def estimate_compression_ratio(file_types: list = None) -> float:
        """
        估算压缩比
        
        Returns:
            压缩比（0.0-1.0，越小压缩效果越好）
        """
        if not file_types:
            return 0.6  # 默认压缩比
        
        # 根据文件类型估算压缩比
        text_extensions = {'.txt', '.log', '.csv', '.xml', '.json', '.html', '.css', '.js'}
        code_extensions = {'.py', '.java', '.cpp', '.c', '.h', '.php', '.rb', '.go'}
        binary_extensions = {'.exe', '.dll', '.so', '.bin'}
        
        text_count = sum(1 for ft in file_types if any(ft.lower().endswith(ext) for ext in text_extensions))
        code_count = sum(1 for ft in file_types if any(ft.lower().endswith(ext) for ext in code_extensions))
        binary_count = sum(1 for ft in file_types if any(ft.lower().endswith(ext) for ext in binary_extensions))
        
        total = len(file_types)
        if total == 0:
            return 0.6
        
        # 文本文件压缩效果好
        if text_count / total > 0.5:
            return 0.3
        # 代码文件压缩效果较好
        elif code_count / total > 0.5:
            return 0.4
        # 二进制文件压缩效果一般
        elif binary_count / total > 0.5:
            return 0.8
        else:
            return 0.6


class NetworkStrategy:
    """网络传输策略"""
    
    @staticmethod
    def estimate_transfer_speed(endpoint_types: tuple) -> int:
        """
        估算传输速度（字节/秒）
        
        Args:
            endpoint_types: (源端点类型, 目标端点类型)
            
        Returns:
            估算的传输速度
        """
        source_type, target_type = endpoint_types
        
        # 本地到本地：磁盘速度
        if source_type == "local" and target_type == "local":
            return 100 * 1024 * 1024  # 100MB/s
        
        # 涉及网络传输：网络速度
        else:
            return 10 * 1024 * 1024   # 10MB/s (假设网络速度)
    
    @staticmethod
    def calculate_transfer_time(size: int, speed: int, compression_ratio: float = 1.0) -> int:
        """
        计算传输时间
        
        Args:
            size: 文件大小（字节）
            speed: 传输速度（字节/秒）
            compression_ratio: 压缩比
            
        Returns:
            估算时间（秒）
        """
        if speed <= 0:
            return 0
        
        effective_size = int(size * compression_ratio)
        return max(1, effective_size // speed)


class LocalToLocalStrategy(TransferStrategy):
    """本地到本地传输策略"""
    
    def estimate_time(self, plan: Dict) -> int:
        """估算传输时间"""
        source_size = plan.get('source_size', 0)
        use_compression = plan.get('use_compression', False)
        
        if use_compression:
            # 压缩 + 解压时间
            compression_time = source_size // (50 * 1024 * 1024)  # 50MB/s压缩速度
            decompression_time = source_size // (100 * 1024 * 1024)  # 100MB/s解压速度
            return compression_time + decompression_time
        else:
            # 直接复制时间
            copy_speed = NetworkStrategy.estimate_transfer_speed(("local", "local"))
            return NetworkStrategy.calculate_transfer_time(source_size, copy_speed)
    
    def optimize_plan(self, plan: Dict) -> Dict:
        """优化本地传输计划"""
        source_size = plan.get('source_size', 0)
        
        # 对于本地传输，小文件不建议压缩
        if source_size < 50 * 1024 * 1024:  # 50MB
            plan['use_compression'] = False
        
        return plan
    
    def validate_plan(self, plan: Dict) -> Tuple[bool, str]:
        """验证本地传输计划"""
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        target_path = plan['target_path']
        source_size = plan.get('source_size', 0)
        
        # 检查目标磁盘空间
        try:
            free_space = target_endpoint.get_free_space(target_path)
            if free_space > 0 and source_size > free_space:
                return False, f"目标磁盘空间不足，需要 {source_size} 字节，可用 {free_space} 字节"
        except:
            pass
        
        return True, "验证通过"


class RemoteTransferStrategy(TransferStrategy):
    """远程传输策略"""
    
    def estimate_time(self, plan: Dict) -> int:
        """估算传输时间"""
        source_size = plan.get('source_size', 0)
        use_compression = plan.get('use_compression', False)
        mode = plan.get('mode')
        
        # 网络传输速度
        network_speed = NetworkStrategy.estimate_transfer_speed(("local", "remote"))
        
        if use_compression:
            compression_ratio = CompressionStrategy.estimate_compression_ratio()
            transfer_time = NetworkStrategy.calculate_transfer_time(
                source_size, network_speed, compression_ratio
            )
            
            # 加上压缩/解压时间
            compression_time = source_size // (30 * 1024 * 1024)  # 30MB/s
            return transfer_time + compression_time
        else:
            return NetworkStrategy.calculate_transfer_time(source_size, network_speed)
    
    def optimize_plan(self, plan: Dict) -> Dict:
        """优化远程传输计划"""
        source_size = plan.get('source_size', 0)
        
        # 远程传输建议使用压缩
        if source_size > 1024 * 1024:  # 1MB以上
            plan['use_compression'] = True
        
        return plan
    
    def validate_plan(self, plan: Dict) -> Tuple[bool, str]:
        """验证远程传输计划"""
        # 检查网络连接
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        
        if hasattr(source_endpoint, 'ssh_client') and not source_endpoint.ssh_client.test_connection():
            return False, "源服务器连接异常"
        
        if hasattr(target_endpoint, 'ssh_client') and not target_endpoint.ssh_client.test_connection():
            return False, "目标服务器连接异常"
        
        return True, "验证通过"


class ServerToServerStrategy(TransferStrategy):
    """服务器间传输策略"""
    
    def estimate_time(self, plan: Dict) -> int:
        """估算传输时间"""
        source_size = plan.get('source_size', 0)
        use_compression = plan.get('use_compression', False)
        
        # 服务器间传输需要两次网络传输
        network_speed = NetworkStrategy.estimate_transfer_speed(("remote", "remote"))
        
        if use_compression:
            compression_ratio = CompressionStrategy.estimate_compression_ratio()
            # 下载 + 上传时间
            transfer_time = 2 * NetworkStrategy.calculate_transfer_time(
                source_size, network_speed, compression_ratio
            )
            
            # 压缩和解压时间
            compression_time = 2 * (source_size // (20 * 1024 * 1024))  # 20MB/s
            return transfer_time + compression_time
        else:
            # 两次传输时间
            return 2 * NetworkStrategy.calculate_transfer_time(source_size, network_speed)
    
    def optimize_plan(self, plan: Dict) -> Dict:
        """优化服务器间传输计划"""
        source_size = plan.get('source_size', 0)
        
        # 服务器间传输强烈建议使用压缩
        if source_size > 512 * 1024:  # 512KB以上
            plan['use_compression'] = True
        
        return plan
    
    def validate_plan(self, plan: Dict) -> Tuple[bool, str]:
        """验证服务器间传输计划"""
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        
        # 检查两个服务器连接
        if not source_endpoint.ssh_client.test_connection():
            return False, "源服务器连接异常"
        
        if not target_endpoint.ssh_client.test_connection():
            return False, "目标服务器连接异常"
        
        # 检查是否为同一服务器
        source_info = source_endpoint.get_server_info()
        target_info = target_endpoint.get_server_info()
        
        if (source_info.get('host') == target_info.get('host') and 
            source_info.get('port') == target_info.get('port')):
            return False, "源服务器和目标服务器不能是同一台"
        
        return True, "验证通过"


class StrategyFactory:
    """策略工厂"""
    
    @staticmethod
    def get_strategy(mode) -> TransferStrategy:
        """根据传输模式获取策略"""
        from .transfer_coordinator import TransferMode
        
        if mode == TransferMode.LOCAL_TO_LOCAL:
            return LocalToLocalStrategy()
        elif mode in [TransferMode.LOCAL_TO_REMOTE, TransferMode.REMOTE_TO_LOCAL]:
            return RemoteTransferStrategy()
        elif mode == TransferMode.REMOTE_TO_REMOTE:
            return ServerToServerStrategy()
        else:
            raise ValueError(f"不支持的传输模式: {mode}")
    
    @staticmethod
    def analyze_transfer(plan: Dict) -> Dict:
        """分析传输计划"""
        mode = plan['mode']
        strategy = StrategyFactory.get_strategy(mode)
        
        # 优化计划
        optimized_plan = strategy.optimize_plan(plan.copy())
        
        # 验证计划
        valid, message = strategy.validate_plan(optimized_plan)
        
        # 估算时间
        estimated_time = strategy.estimate_time(optimized_plan)
        
        analysis = {
            'valid': valid,
            'message': message,
            'estimated_time': estimated_time,
            'optimized_plan': optimized_plan,
            'recommendations': []
        }
        
        # 添加建议
        if optimized_plan.get('use_compression') != plan.get('use_compression'):
            if optimized_plan.get('use_compression'):
                analysis['recommendations'].append("建议启用压缩以减少传输时间")
            else:
                analysis['recommendations'].append("建议禁用压缩以减少处理时间")
        
        return analysis
