"""
远程端点
管理SSH连接和远程文件系统操作
"""
import stat
from typing import List, Dict, Tuple, Optional
from .base_endpoint import BaseEndpoint, PathUtils

try:
    from ..ssh_client import SSHClient
    from ..config_manager import ConfigManager
except ImportError:
    from ssh_client import SSHClient
    from config_manager import ConfigManager


class RemoteEndpoint(BaseEndpoint):
    """远程SSH端点"""
    
    def __init__(self, server_name: str = None, config_manager: ConfigManager = None):
        super().__init__("remote")
        self.server_name = server_name
        self.config_manager = config_manager or ConfigManager()
        self.ssh_client = SSHClient()
        self.server_config = None
    
    def set_server(self, server_name: str):
        """设置服务器"""
        self.server_name = server_name
        self.server_config = None
        if self.connected:
            self.disconnect()
    
    def connect(self) -> <PERSON><PERSON>[bool, str]:
        """连接到远程服务器"""
        if not self.server_name:
            return False, "未指定服务器"
        
        # 获取服务器配置
        self.server_config = self.config_manager.get_server(self.server_name)
        if not self.server_config:
            return False, f"服务器配置不存在: {self.server_name}"
        
        # 建立SSH连接
        success, message = self.ssh_client.connect(
            self.server_config['host'],
            self.server_config['port'],
            self.server_config['username'],
            self.server_config['password']
        )
        
        if success:
            self.connected = True
            return True, f"已连接到 {self.server_name}"
        else:
            self.connected = False
            return False, f"连接失败: {message}"
    
    def disconnect(self):
        """断开SSH连接"""
        if self.ssh_client:
            self.ssh_client.disconnect()
        self.connected = False
    
    def list_directory(self, path: str) -> Tuple[bool, List[Dict]]:
        """列出远程目录内容"""
        if not self.connected:
            return False, "未连接到服务器"
        
        return self.ssh_client.list_remote_directory(path)
    
    def path_exists(self, path: str) -> bool:
        """检查远程路径是否存在"""
        if not self.connected:
            return False
        
        try:
            self.ssh_client.sftp.stat(path)
            return True
        except FileNotFoundError:
            return False
        except Exception:
            return False
    
    def is_directory(self, path: str) -> bool:
        """检查是否为目录"""
        if not self.connected:
            return False
        
        try:
            stat_info = self.ssh_client.sftp.stat(path)
            return stat.S_ISDIR(stat_info.st_mode)
        except:
            return False
    
    def get_path_info(self, path: str) -> Dict:
        """获取远程路径信息"""
        if not self.connected:
            return {'error': '未连接到服务器'}
        
        try:
            stat_info = self.ssh_client.sftp.stat(path)
            return {
                'size': stat_info.st_size,
                'modified': stat_info.st_mtime,
                'is_dir': stat.S_ISDIR(stat_info.st_mode),
                'is_file': stat.S_ISREG(stat_info.st_mode),
                'permissions': oct(stat_info.st_mode)[-3:],
                'owner_readable': bool(stat_info.st_mode & stat.S_IRUSR),
                'owner_writable': bool(stat_info.st_mode & stat.S_IWUSR),
                'owner_executable': bool(stat_info.st_mode & stat.S_IXUSR)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def create_directory(self, path: str) -> bool:
        """创建远程目录"""
        if not self.connected:
            return False
        
        return self.ssh_client.create_remote_directory(path)
    
    def remove_path(self, path: str) -> bool:
        """删除远程文件或目录"""
        if not self.connected:
            return False
        
        try:
            # 检查是否为目录
            if self.is_directory(path):
                # 删除目录（递归）
                rm_cmd = f"rm -rf '{path}'"
            else:
                # 删除文件
                rm_cmd = f"rm -f '{path}'"
            
            stdin, stdout, stderr = self.ssh_client.ssh.exec_command(rm_cmd)
            exit_status = stdout.channel.recv_exit_status()
            return exit_status == 0
            
        except Exception as e:
            print(f"删除远程路径失败: {e}")
            return False
    
    def execute_command(self, command: str) -> Tuple[bool, str, str]:
        """
        执行远程命令
        
        Returns:
            (成功标志, 标准输出, 错误输出)
        """
        if not self.connected:
            return False, "", "未连接到服务器"
        
        try:
            stdin, stdout, stderr = self.ssh_client.ssh.exec_command(command)
            exit_status = stdout.channel.recv_exit_status()
            
            stdout_text = stdout.read().decode('utf-8', errors='ignore')
            stderr_text = stderr.read().decode('utf-8', errors='ignore')
            
            return exit_status == 0, stdout_text, stderr_text
            
        except Exception as e:
            return False, "", str(e)
    
    def get_free_space(self, path: str) -> int:
        """获取远程路径所在文件系统的可用空间"""
        if not self.connected:
            return 0
        
        try:
            # 使用df命令获取可用空间
            cmd = f"df -B1 '{path}' | tail -1 | awk '{{print $4}}'"
            success, output, error = self.execute_command(cmd)
            
            if success and output.strip().isdigit():
                return int(output.strip())
        except:
            pass
        return 0
    
    def get_directory_size(self, path: str) -> int:
        """获取远程目录总大小"""
        if not self.connected:
            return 0
        
        try:
            # 使用du命令获取目录大小
            cmd = f"du -sb '{path}' | cut -f1"
            success, output, error = self.execute_command(cmd)
            
            if success and output.strip().isdigit():
                return int(output.strip())
        except:
            pass
        return 0
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.server_name:
            return f"远程服务器: {self.server_name}"
        return "远程服务器"
    
    def get_home_directory(self) -> str:
        """获取远程用户主目录"""
        if not self.connected:
            return "/"
        
        try:
            success, output, error = self.execute_command("echo $HOME")
            if success and output.strip():
                return output.strip()
        except:
            pass
        return "/"
    
    def get_current_directory(self) -> str:
        """获取远程当前工作目录"""
        if not self.connected:
            return "/"
        
        try:
            success, output, error = self.execute_command("pwd")
            if success and output.strip():
                return output.strip()
        except:
            pass
        return "/"
    
    def normalize_path(self, path: str) -> str:
        """标准化远程路径"""
        return PathUtils.normalize_remote_path(path)

    def join_path(self, *parts) -> str:
        """连接远程路径"""
        return PathUtils.join_remote_path(*parts)

    def get_parent_directory(self, path: str) -> str:
        """获取父目录"""
        return PathUtils.get_remote_parent(path)

    def get_basename(self, path: str) -> str:
        """获取路径的基本名称"""
        return PathUtils.get_remote_basename(path)
    
    def get_server_info(self) -> Dict:
        """获取服务器信息"""
        info = {
            'server_name': self.server_name,
            'connected': self.connected
        }
        
        if self.server_config:
            info.update({
                'host': self.server_config['host'],
                'port': self.server_config['port'],
                'username': self.server_config['username']
            })
        
        return info
