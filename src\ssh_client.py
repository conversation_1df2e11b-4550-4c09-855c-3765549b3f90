"""
SSH文件传输客户端
基于paramiko实现SSH连接和SFTP文件传输
"""
import os
import stat
import paramiko
from typing import Callable, Optional, Tuple
import threading
import time


class SSHClient:
    """SSH客户端类"""
    
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connected = False
        self.cancel_transfer = False
    
    def connect(self, host: str, port: int, username: str, password: str, timeout: int = 30) -> Tuple[bool, str]:
        """连接到SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            self.ssh.connect(
                hostname=host,
                port=port,
                username=username,
                password=password,
                timeout=timeout
            )
            
            self.sftp = self.ssh.open_sftp()
            self.connected = True
            return True, "连接成功"
            
        except paramiko.AuthenticationException:
            return False, "认证失败：用户名或密码错误"
        except paramiko.SSHException as e:
            return False, f"SSH连接错误：{str(e)}"
        except Exception as e:
            return False, f"连接失败：{str(e)}"
    
    def disconnect(self):
        """断开SSH连接"""
        try:
            if self.sftp:
                self.sftp.close()
                self.sftp = None
            if self.ssh:
                self.ssh.close()
                self.ssh = None
            self.connected = False
        except Exception as e:
            print(f"断开连接时出错：{e}")
    
    def test_connection(self) -> bool:
        """测试连接是否有效"""
        if not self.connected or not self.ssh:
            return False
        try:
            self.ssh.exec_command('echo "test"', timeout=5)
            return True
        except:
            return False
    
    def list_remote_directory(self, remote_path: str) -> Tuple[bool, list]:
        """列出远程目录内容"""
        if not self.connected:
            return False, []
        
        try:
            files = []
            for item in self.sftp.listdir_attr(remote_path):
                file_info = {
                    'name': item.filename,
                    'size': item.st_size,
                    'is_dir': stat.S_ISDIR(item.st_mode),
                    'modified': item.st_mtime
                }
                files.append(file_info)
            return True, files
        except Exception as e:
            return False, f"列出目录失败：{str(e)}"
    
    def create_remote_directory(self, remote_path: str) -> bool:
        """创建远程目录"""
        if not self.connected:
            return False
        
        try:
            self.sftp.mkdir(remote_path)
            return True
        except Exception as e:
            print(f"创建远程目录失败：{e}")
            return False
    
    def download_file(self, remote_path: str, local_path: str, 
                     progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """下载单个文件"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            # 确保本地目录存在
            local_dir = os.path.dirname(local_path)
            if local_dir and not os.path.exists(local_dir):
                os.makedirs(local_dir)
            
            # 获取文件大小
            file_size = self.sftp.stat(remote_path).st_size
            
            def progress_wrapper(transferred, total):
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                if progress_callback:
                    progress_callback(transferred, total)
            
            self.sftp.get(remote_path, local_path, callback=progress_wrapper)
            return True, "下载成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"下载失败：{str(e)}"
    
    def upload_file(self, local_path: str, remote_path: str, 
                   progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """上传单个文件"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            # 确保远程目录存在
            remote_dir = os.path.dirname(remote_path).replace('\\', '/')
            if remote_dir:
                try:
                    self.sftp.stat(remote_dir)
                except FileNotFoundError:
                    self._create_remote_path(remote_dir)
            
            def progress_wrapper(transferred, total):
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                if progress_callback:
                    progress_callback(transferred, total)
            
            self.sftp.put(local_path, remote_path, callback=progress_wrapper)
            return True, "上传成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"上传失败：{str(e)}"
    
    def _create_remote_path(self, remote_path: str):
        """递归创建远程路径"""
        parts = remote_path.split('/')
        current_path = ""
        
        for part in parts:
            if not part:
                continue
            current_path += "/" + part if current_path else part
            try:
                self.sftp.stat(current_path)
            except FileNotFoundError:
                self.sftp.mkdir(current_path)
    
    def download_directory(self, remote_dir: str, local_dir: str, 
                          progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """下载整个目录"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            self.cancel_transfer = False
            total_files = self._count_remote_files(remote_dir)
            processed_files = 0
            
            def download_recursive(remote_path: str, local_path: str):
                nonlocal processed_files
                
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                
                # 确保本地目录存在
                if not os.path.exists(local_path):
                    os.makedirs(local_path)
                
                # 列出远程目录内容
                for item in self.sftp.listdir_attr(remote_path):
                    if self.cancel_transfer:
                        raise Exception("传输已取消")
                    
                    remote_item_path = f"{remote_path}/{item.filename}"
                    local_item_path = os.path.join(local_path, item.filename)
                    
                    if stat.S_ISDIR(item.st_mode):
                        # 递归下载子目录
                        download_recursive(remote_item_path, local_item_path)
                    else:
                        # 下载文件
                        def file_progress(transferred, total):
                            if progress_callback:
                                overall_progress = (processed_files + transferred / total) / total_files
                                progress_callback(overall_progress * 100, 100)
                        
                        success, msg = self.download_file(remote_item_path, local_item_path, file_progress)
                        if not success:
                            raise Exception(f"下载文件失败：{msg}")
                        
                        processed_files += 1
                        if progress_callback:
                            progress_callback(processed_files / total_files * 100, 100)
            
            download_recursive(remote_dir, local_dir)
            return True, "目录下载成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"目录下载失败：{str(e)}"
    
    def upload_directory(self, local_dir: str, remote_dir: str, 
                        progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """上传整个目录"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            self.cancel_transfer = False
            total_files = self._count_local_files(local_dir)
            processed_files = 0
            
            def upload_recursive(local_path: str, remote_path: str):
                nonlocal processed_files
                
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                
                # 确保远程目录存在
                try:
                    self.sftp.stat(remote_path)
                except FileNotFoundError:
                    self.sftp.mkdir(remote_path)
                
                # 遍历本地目录
                for item in os.listdir(local_path):
                    if self.cancel_transfer:
                        raise Exception("传输已取消")
                    
                    local_item_path = os.path.join(local_path, item)
                    remote_item_path = f"{remote_path}/{item}"
                    
                    if os.path.isdir(local_item_path):
                        # 递归上传子目录
                        upload_recursive(local_item_path, remote_item_path)
                    else:
                        # 上传文件
                        def file_progress(transferred, total):
                            if progress_callback:
                                overall_progress = (processed_files + transferred / total) / total_files
                                progress_callback(overall_progress * 100, 100)
                        
                        success, msg = self.upload_file(local_item_path, remote_item_path, file_progress)
                        if not success:
                            raise Exception(f"上传文件失败：{msg}")
                        
                        processed_files += 1
                        if progress_callback:
                            progress_callback(processed_files / total_files * 100, 100)
            
            upload_recursive(local_dir, remote_dir)
            return True, "目录上传成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"目录上传失败：{str(e)}"
    
    def _count_remote_files(self, remote_dir: str) -> int:
        """统计远程目录中的文件数量"""
        count = 0
        try:
            for item in self.sftp.listdir_attr(remote_dir):
                if stat.S_ISDIR(item.st_mode):
                    count += self._count_remote_files(f"{remote_dir}/{item.filename}")
                else:
                    count += 1
        except:
            pass
        return count
    
    def _count_local_files(self, local_dir: str) -> int:
        """统计本地目录中的文件数量"""
        count = 0
        try:
            for item in os.listdir(local_dir):
                item_path = os.path.join(local_dir, item)
                if os.path.isdir(item_path):
                    count += self._count_local_files(item_path)
                else:
                    count += 1
        except:
            pass
        return count
    
    def cancel_current_transfer(self):
        """取消当前传输"""
        self.cancel_transfer = True

    def execute_command(self, command: str, timeout: int = 30) -> Tuple[bool, str, str]:
        """
        执行远程命令

        Args:
            command: 要执行的命令
            timeout: 超时时间（秒）

        Returns:
            (成功标志, 标准输出, 错误输出)
        """
        if not self.connected:
            return False, "", "未连接到服务器"

        try:
            stdin, stdout, stderr = self.ssh.exec_command(command, timeout=timeout)
            exit_status = stdout.channel.recv_exit_status()

            stdout_text = stdout.read().decode('utf-8', errors='ignore')
            stderr_text = stderr.read().decode('utf-8', errors='ignore')

            return exit_status == 0, stdout_text, stderr_text

        except Exception as e:
            return False, "", str(e)

    def transfer_to_remote_server(self, source_path: str, target_host: str, target_port: int,
                                 target_username: str, target_password: str, target_path: str,
                                 use_compression: bool = True, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        从当前服务器直接传输文件到另一个服务器

        Args:
            source_path: 源文件/目录路径
            target_host: 目标服务器地址
            target_port: 目标服务器端口
            target_username: 目标服务器用户名
            target_password: 目标服务器密码
            target_path: 目标路径
            use_compression: 是否使用压缩
            progress_callback: 进度回调函数

        Returns:
            (成功标志, 消息)
        """
        if not self.connected:
            return False, "源服务器未连接"

        try:
            # 检查源路径是否存在
            success, stdout, stderr = self.execute_command(f"test -e '{source_path}' && echo 'exists' || echo 'not_exists'")
            if not success or 'not_exists' in stdout:
                return False, f"源路径不存在: {source_path}"

            # 检查是否为目录
            success, stdout, stderr = self.execute_command(f"test -d '{source_path}' && echo 'dir' || echo 'file'")
            is_directory = success and 'dir' in stdout

            if progress_callback:
                progress_callback(10, 100, "准备传输...")

            if use_compression and is_directory:
                # 使用压缩传输目录
                return self._transfer_compressed_directory(
                    source_path, target_host, target_port, target_username,
                    target_password, target_path, progress_callback
                )
            else:
                # 直接传输文件或目录
                return self._transfer_direct(
                    source_path, target_host, target_port, target_username,
                    target_password, target_path, is_directory, progress_callback
                )

        except Exception as e:
            return False, f"服务器间传输失败: {str(e)}"

    def _transfer_compressed_directory(self, source_path: str, target_host: str, target_port: int,
                                     target_username: str, target_password: str, target_path: str,
                                     progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """使用压缩方式传输目录"""
        try:
            # 创建临时压缩文件，处理特殊字符
            basename = os.path.basename(source_path)
            safe_basename = "".join(c for c in basename if c.isalnum() or c in ('-', '_', '.'))
            if not safe_basename:
                safe_basename = "transfer"
            temp_archive = f"/tmp/{safe_basename}_{int(time.time())}.tar.gz"

            if progress_callback:
                progress_callback(15, 100, "压缩源目录...")

            # 在源服务器压缩，使用双引号处理包含空格的路径
            parent_dir = os.path.dirname(source_path)
            source_name = os.path.basename(source_path)
            compress_cmd = f'cd "{parent_dir}" && tar -czf "{temp_archive}" "{source_name}"'
            success, stdout, stderr = self.execute_command(compress_cmd, timeout=300)

            if not success:
                return False, f"压缩失败: {stderr}"

            if progress_callback:
                progress_callback(40, 100, "传输压缩文件...")

            # 使用scp直接传输到目标服务器，使用双引号处理包含空格的路径
            target_temp_archive = f"/tmp/{os.path.basename(temp_archive)}"
            scp_cmd = f'sshpass -p "{target_password}" scp -P {target_port} -o StrictHostKeyChecking=no "{temp_archive}" "{target_username}@{target_host}:{target_temp_archive}"'

            success, stdout, stderr = self.execute_command(scp_cmd, timeout=600)

            if not success:
                # 清理源服务器临时文件
                self.execute_command(f'rm -f "{temp_archive}"')
                return False, f"传输失败: {stderr}"

            if progress_callback:
                progress_callback(80, 100, "在目标服务器解压...")

            # 在目标服务器解压，使用双引号处理包含空格的路径
            decompress_cmd = f'sshpass -p "{target_password}" ssh -p {target_port} -o StrictHostKeyChecking=no "{target_username}@{target_host}" "mkdir -p \\"{target_path}\\" && cd \\"{target_path}\\" && tar -xzf \\"{target_temp_archive}\\" && rm -f \\"{target_temp_archive}\\""'

            success, stdout, stderr = self.execute_command(decompress_cmd, timeout=300)

            # 清理源服务器临时文件
            self.execute_command(f'rm -f "{temp_archive}"')

            if not success:
                return False, f"解压失败: {stderr}"

            if progress_callback:
                progress_callback(100, 100, "传输完成")

            return True, "压缩传输完成"

        except Exception as e:
            return False, f"压缩传输失败: {str(e)}"

    def _transfer_direct(self, source_path: str, target_host: str, target_port: int,
                        target_username: str, target_password: str, target_path: str,
                        is_directory: bool, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """直接传输文件或目录"""
        try:
            if progress_callback:
                progress_callback(20, 100, "直接传输...")

            if is_directory:
                # 传输目录，使用双引号处理包含空格的路径
                scp_cmd = f'sshpass -p "{target_password}" scp -r -P {target_port} -o StrictHostKeyChecking=no "{source_path}" "{target_username}@{target_host}:{target_path}"'
            else:
                # 传输文件，使用双引号处理包含空格的路径
                scp_cmd = f'sshpass -p "{target_password}" scp -P {target_port} -o StrictHostKeyChecking=no "{source_path}" "{target_username}@{target_host}:{target_path}"'

            success, stdout, stderr = self.execute_command(scp_cmd, timeout=600)

            if not success:
                return False, f"直接传输失败: {stderr}"

            if progress_callback:
                progress_callback(100, 100, "传输完成")

            return True, "直接传输完成"

        except Exception as e:
            return False, f"直接传输失败: {str(e)}"
