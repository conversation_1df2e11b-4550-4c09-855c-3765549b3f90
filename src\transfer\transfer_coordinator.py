"""
传输协调器
协调不同端点之间的文件传输
"""
import os
import tempfile
import threading
from typing import Optional, Callable, Tuple, Dict
from enum import Enum

try:
    from ..endpoints.base_endpoint import BaseEndpoint, PathUtils
    from ..endpoints.local_endpoint import LocalEndpoint
    from ..endpoints.remote_endpoint import RemoteEndpoint
    from ..compression.tar_compression import TarCompressionManager
except ImportError:
    from endpoints.base_endpoint import BaseEndpoint, PathUtils
    from endpoints.local_endpoint import LocalEndpoint
    from endpoints.remote_endpoint import RemoteEndpoint
    from compression.tar_compression import TarCompressionManager


class TransferMode(Enum):
    """传输模式枚举"""
    LOCAL_TO_LOCAL = "local_to_local"
    LOCAL_TO_REMOTE = "local_to_remote"
    REMOTE_TO_LOCAL = "remote_to_local"
    REMOTE_TO_REMOTE = "remote_to_remote"


class TransferCoordinator:
    """传输协调器"""
    
    def __init__(self):
        self.compression_manager = TarCompressionManager()
        self.cancel_transfer = False
        self.current_transfer = None
        
    def plan_transfer(self, source_endpoint: BaseEndpoint, source_path: str,
                     target_endpoint: BaseEndpoint, target_path: str,
                     use_compression: bool = True) -> Dict:
        """
        规划传输策略
        
        Args:
            source_endpoint: 源端点
            source_path: 源路径
            target_endpoint: 目标端点
            target_path: 目标路径
            use_compression: 是否使用压缩
            
        Returns:
            传输计划字典
        """
        # 确定传输模式
        if isinstance(source_endpoint, LocalEndpoint) and isinstance(target_endpoint, LocalEndpoint):
            mode = TransferMode.LOCAL_TO_LOCAL
        elif isinstance(source_endpoint, LocalEndpoint) and isinstance(target_endpoint, RemoteEndpoint):
            mode = TransferMode.LOCAL_TO_REMOTE
        elif isinstance(source_endpoint, RemoteEndpoint) and isinstance(target_endpoint, LocalEndpoint):
            mode = TransferMode.REMOTE_TO_LOCAL
        elif isinstance(source_endpoint, RemoteEndpoint) and isinstance(target_endpoint, RemoteEndpoint):
            mode = TransferMode.REMOTE_TO_REMOTE
        else:
            raise ValueError("不支持的端点类型组合")
        
        # 验证端点连接
        if not source_endpoint.is_connected():
            raise ValueError("源端点未连接")
        if not target_endpoint.is_connected():
            raise ValueError("目标端点未连接")

        # 标准化路径
        source_path = source_endpoint.normalize_path(source_path)
        target_path = target_endpoint.normalize_path(target_path)

        # 验证源路径
        if not source_endpoint.path_exists(source_path):
            raise ValueError(f"源路径不存在: {source_path}")

        # 获取源路径信息
        source_info = source_endpoint.get_path_info(source_path)
        is_directory = source_info.get('is_dir', False)
        
        # 创建传输计划
        plan = {
            'mode': mode,
            'source_endpoint': source_endpoint,
            'source_path': source_path,
            'target_endpoint': target_endpoint,
            'target_path': target_path,
            'use_compression': use_compression,
            'is_directory': is_directory,
            'source_size': source_info.get('size', 0),
            'temp_files': []
        }
        
        return plan
    
    def execute_transfer(self, plan: Dict, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        执行传输计划
        
        Args:
            plan: 传输计划
            progress_callback: 进度回调函数 (current, total, message)
            
        Returns:
            (成功标志, 消息)
        """
        try:
            self.cancel_transfer = False
            self.current_transfer = plan
            
            mode = plan['mode']
            use_compression = plan['use_compression']
            
            if progress_callback:
                progress_callback(0, 100, "开始传输...")
            
            # 根据传输模式执行相应策略
            if mode == TransferMode.LOCAL_TO_LOCAL:
                return self._execute_local_to_local(plan, progress_callback)
            elif mode == TransferMode.LOCAL_TO_REMOTE:
                return self._execute_local_to_remote(plan, progress_callback)
            elif mode == TransferMode.REMOTE_TO_LOCAL:
                return self._execute_remote_to_local(plan, progress_callback)
            elif mode == TransferMode.REMOTE_TO_REMOTE:
                return self._execute_remote_to_remote(plan, progress_callback)
            else:
                return False, f"不支持的传输模式: {mode}"
                
        except Exception as e:
            if "已取消" in str(e):
                return False, "传输已取消"
            return False, f"传输失败: {str(e)}"
        finally:
            self._cleanup_temp_files(plan)
            self.current_transfer = None
    
    def _execute_local_to_local(self, plan: Dict, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """执行本地到本地传输"""
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        source_path = plan['source_path']
        target_path = plan['target_path']
        use_compression = plan['use_compression']
        is_directory = plan['is_directory']
        
        if self.cancel_transfer:
            raise Exception("传输已取消")
        
        if use_compression:
            # 使用压缩传输
            if progress_callback:
                progress_callback(10, 100, "压缩源文件...")
            
            # 压缩源文件
            success, message, archive_path = self.compression_manager.compress_directory(
                source_path,
                progress_callback=lambda c, t, f: progress_callback(10 + (c/t)*30, 100, f"压缩: {f}") if progress_callback else None
            )
            
            if not success:
                return False, f"压缩失败: {message}"
            
            plan['temp_files'].append(archive_path)
            
            if progress_callback:
                progress_callback(50, 100, "解压到目标位置...")
            
            # 解压到目标位置
            success, message = self.compression_manager.decompress_archive(
                archive_path, target_path,
                progress_callback=lambda c, t, f: progress_callback(50 + (c/t)*40, 100, f"解压: {f}") if progress_callback else None
            )
            
            if not success:
                return False, f"解压失败: {message}"
            
        else:
            # 直接复制
            if progress_callback:
                progress_callback(20, 100, "复制文件...")
            
            if is_directory:
                success = source_endpoint.copy_directory(source_path, target_path)
            else:
                success = source_endpoint.copy_file(source_path, target_path)
            
            if not success:
                return False, "文件复制失败"
        
        if progress_callback:
            progress_callback(100, 100, "传输完成")
        
        return True, "本地传输完成"
    
    def _execute_local_to_remote(self, plan: Dict, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """执行本地到远程传输"""
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        source_path = plan['source_path']
        target_path = plan['target_path']
        use_compression = plan['use_compression']
        is_directory = plan['is_directory']
        
        if self.cancel_transfer:
            raise Exception("传输已取消")
        
        if use_compression:
            # 压缩传输
            if progress_callback:
                progress_callback(10, 100, "压缩本地文件...")
            
            # 本地压缩
            success, message, archive_path = self.compression_manager.compress_directory(
                source_path,
                progress_callback=lambda c, t, f: progress_callback(10 + (c/t)*30, 100, f"压缩: {f}") if progress_callback else None
            )
            
            if not success:
                return False, f"压缩失败: {message}"
            
            plan['temp_files'].append(archive_path)
            
            if progress_callback:
                progress_callback(50, 100, "上传压缩文件...")
            
            # 上传压缩文件
            remote_temp_archive = f"/tmp/{os.path.basename(archive_path)}"
            ssh_client = target_endpoint.ssh_client
            
            success, message = ssh_client.upload_file(
                archive_path, remote_temp_archive,
                progress_callback=lambda c, t: progress_callback(50 + (c/t)*30, 100, "上传中...") if progress_callback else None
            )
            
            if not success:
                return False, f"上传失败: {message}"
            
            plan['temp_files'].append(remote_temp_archive)
            
            if progress_callback:
                progress_callback(85, 100, "远程解压...")
            
            # 远程解压
            success, message = self.compression_manager.decompress_remote_archive(
                ssh_client, remote_temp_archive, target_path
            )
            
            if not success:
                return False, f"远程解压失败: {message}"
            
        else:
            # 直接上传
            if progress_callback:
                progress_callback(20, 100, "上传文件...")
            
            ssh_client = target_endpoint.ssh_client
            if is_directory:
                success, message = ssh_client.upload_directory(
                    source_path, target_path,
                    progress_callback=lambda c, t: progress_callback(20 + (c/t)*70, 100, "上传中...") if progress_callback else None
                )
            else:
                success, message = ssh_client.upload_file(
                    source_path, target_path,
                    progress_callback=lambda c, t: progress_callback(20 + (c/t)*70, 100, "上传中...") if progress_callback else None
                )
            
            if not success:
                return False, f"上传失败: {message}"
        
        if progress_callback:
            progress_callback(100, 100, "传输完成")
        
        return True, "上传传输完成"
    
    def _execute_remote_to_local(self, plan: Dict, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """执行远程到本地传输"""
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        source_path = plan['source_path']
        target_path = plan['target_path']
        use_compression = plan['use_compression']
        is_directory = plan['is_directory']
        
        if self.cancel_transfer:
            raise Exception("传输已取消")
        
        if use_compression:
            # 压缩传输
            if progress_callback:
                progress_callback(10, 100, "远程压缩...")
            
            # 远程压缩
            ssh_client = source_endpoint.ssh_client
            success, message, remote_archive = self.compression_manager.compress_remote_directory(
                ssh_client, source_path
            )
            
            if not success:
                return False, f"远程压缩失败: {message}"
            
            plan['temp_files'].append(remote_archive)
            
            if progress_callback:
                progress_callback(40, 100, "下载压缩文件...")
            
            # 下载压缩文件
            local_temp_dir = self.compression_manager.get_temp_directory()
            local_archive = os.path.join(local_temp_dir, os.path.basename(remote_archive))
            
            success, message = ssh_client.download_file(
                remote_archive, local_archive,
                progress_callback=lambda c, t: progress_callback(40 + (c/t)*30, 100, "下载中...") if progress_callback else None
            )
            
            if not success:
                return False, f"下载失败: {message}"
            
            plan['temp_files'].append(local_archive)
            
            if progress_callback:
                progress_callback(75, 100, "本地解压...")

            # 确保目标目录存在
            target_endpoint.create_directory(target_path)

            # 验证目标路径
            if not os.path.exists(target_path):
                return False, f"目标目录创建失败: {target_path}"

            if progress_callback:
                progress_callback(75, 100, f"解压到: {target_path}")

            # 本地解压
            success, message = self.compression_manager.decompress_archive(
                local_archive, target_path,
                progress_callback=lambda c, t, f: progress_callback(75 + (c/t)*20, 100, f"解压: {f}") if progress_callback else None
            )

            if not success:
                return False, f"解压失败: {message}"

            # 验证解压结果
            if os.path.exists(target_path):
                files_count = sum([len(files) for r, d, files in os.walk(target_path)])
                if progress_callback:
                    progress_callback(95, 100, f"解压完成，共 {files_count} 个文件到 {target_path}")
            else:
                return False, f"解压后目标目录不存在: {target_path}"
            
        else:
            # 直接下载
            if progress_callback:
                progress_callback(20, 100, "下载文件...")
            
            ssh_client = source_endpoint.ssh_client
            if is_directory:
                success, message = ssh_client.download_directory(
                    source_path, target_path,
                    progress_callback=lambda c, t: progress_callback(20 + (c/t)*70, 100, "下载中...") if progress_callback else None
                )
            else:
                success, message = ssh_client.download_file(
                    source_path, target_path,
                    progress_callback=lambda c, t: progress_callback(20 + (c/t)*70, 100, "下载中...") if progress_callback else None
                )
            
            if not success:
                return False, f"下载失败: {message}"
        
        if progress_callback:
            progress_callback(100, 100, "传输完成")
        
        return True, "下载传输完成"

    def _execute_remote_to_remote(self, plan: Dict, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """执行远程到远程传输"""
        source_endpoint = plan['source_endpoint']
        target_endpoint = plan['target_endpoint']
        source_path = plan['source_path']
        target_path = plan['target_path']
        use_compression = plan['use_compression']
        is_directory = plan['is_directory']

        if self.cancel_transfer:
            raise Exception("传输已取消")

        # 获取目标服务器配置
        target_config = target_endpoint.get_server_info()
        if not target_config or 'host' not in target_config:
            return False, "无法获取目标服务器配置"

        # 尝试服务器间直接传输
        source_ssh = source_endpoint.ssh_client

        # 检查源服务器是否安装了sshpass（用于自动化SSH连接）
        success, stdout, stderr = source_ssh.execute_command("which sshpass")

        if success and stdout.strip():
            # 使用服务器间直接传输
            if progress_callback:
                progress_callback(5, 100, "开始服务器间直接传输...")

            # 获取目标服务器配置
            target_server_config = target_endpoint.server_config

            success, message = source_ssh.transfer_to_remote_server(
                source_path,
                target_server_config['host'],
                target_server_config['port'],
                target_server_config['username'],
                target_server_config['password'],
                target_path,
                use_compression,
                progress_callback
            )

            if success:
                return True, f"服务器间直接传输完成: {message}"
            else:
                # 如果直接传输失败，回退到本地中转模式
                if progress_callback:
                    progress_callback(0, 100, "直接传输失败，使用本地中转模式...")

        # 回退到本地中转模式
        if use_compression:
            # 压缩传输
            if progress_callback:
                progress_callback(5, 100, "源服务器压缩...")

            # 在源服务器压缩
            source_ssh = source_endpoint.ssh_client
            success, message, remote_archive = self.compression_manager.compress_remote_directory(
                source_ssh, source_path
            )

            if not success:
                return False, f"源服务器压缩失败: {message}"

            plan['temp_files'].append(remote_archive)

            if progress_callback:
                progress_callback(25, 100, "下载到本地中转...")

            # 下载到本地临时目录
            local_temp_dir = self.compression_manager.get_temp_directory()
            local_archive = os.path.join(local_temp_dir, os.path.basename(remote_archive))

            success, message = source_ssh.download_file(
                remote_archive, local_archive,
                progress_callback=lambda c, t: progress_callback(25 + (c/t)*25, 100, "下载中...") if progress_callback else None
            )

            if not success:
                return False, f"下载到本地失败: {message}"

            plan['temp_files'].append(local_archive)

            if progress_callback:
                progress_callback(55, 100, "上传到目标服务器...")

            # 上传到目标服务器
            target_ssh = target_endpoint.ssh_client
            target_temp_archive = f"/tmp/{os.path.basename(local_archive)}"

            success, message = target_ssh.upload_file(
                local_archive, target_temp_archive,
                progress_callback=lambda c, t: progress_callback(55 + (c/t)*25, 100, "上传中...") if progress_callback else None
            )

            if not success:
                return False, f"上传到目标服务器失败: {message}"

            plan['temp_files'].append(target_temp_archive)

            if progress_callback:
                progress_callback(85, 100, "目标服务器解压...")

            # 在目标服务器解压
            success, message = self.compression_manager.decompress_remote_archive(
                target_ssh, target_temp_archive, target_path
            )

            if not success:
                return False, f"目标服务器解压失败: {message}"

        else:
            # 直接传输（通过本地中转）
            if progress_callback:
                progress_callback(10, 100, "下载到本地中转...")

            # 先下载到本地临时目录
            local_temp_dir = self.compression_manager.get_temp_directory()
            local_temp_path = os.path.join(local_temp_dir, os.path.basename(source_path))

            source_ssh = source_endpoint.ssh_client
            if is_directory:
                success, message = source_ssh.download_directory(
                    source_path, local_temp_path,
                    progress_callback=lambda c, t: progress_callback(10 + (c/t)*35, 100, "下载中...") if progress_callback else None
                )
            else:
                success, message = source_ssh.download_file(
                    source_path, local_temp_path,
                    progress_callback=lambda c, t: progress_callback(10 + (c/t)*35, 100, "下载中...") if progress_callback else None
                )

            if not success:
                return False, f"下载到本地失败: {message}"

            plan['temp_files'].append(local_temp_path)

            if progress_callback:
                progress_callback(50, 100, "上传到目标服务器...")

            # 再上传到目标服务器
            target_ssh = target_endpoint.ssh_client
            if is_directory:
                success, message = target_ssh.upload_directory(
                    local_temp_path, target_path,
                    progress_callback=lambda c, t: progress_callback(50 + (c/t)*40, 100, "上传中...") if progress_callback else None
                )
            else:
                success, message = target_ssh.upload_file(
                    local_temp_path, target_path,
                    progress_callback=lambda c, t: progress_callback(50 + (c/t)*40, 100, "上传中...") if progress_callback else None
                )

            if not success:
                return False, f"上传到目标服务器失败: {message}"

        if progress_callback:
            progress_callback(100, 100, "传输完成")

        return True, "服务器间传输完成"

    def _cleanup_temp_files(self, plan: Dict):
        """清理临时文件"""
        temp_files = plan.get('temp_files', [])

        for temp_file in temp_files:
            try:
                if temp_file.startswith('/tmp/'):
                    # 远程临时文件
                    if 'source_endpoint' in plan and hasattr(plan['source_endpoint'], 'ssh_client'):
                        self.compression_manager.cleanup_remote_file(plan['source_endpoint'].ssh_client, temp_file)
                    if 'target_endpoint' in plan and hasattr(plan['target_endpoint'], 'ssh_client'):
                        self.compression_manager.cleanup_remote_file(plan['target_endpoint'].ssh_client, temp_file)
                else:
                    # 本地临时文件
                    if os.path.exists(temp_file):
                        if os.path.isfile(temp_file):
                            os.remove(temp_file)
                        elif os.path.isdir(temp_file):
                            import shutil
                            shutil.rmtree(temp_file)
            except Exception as e:
                print(f"清理临时文件失败 {temp_file}: {e}")

        # 清理压缩管理器的临时目录
        self.compression_manager.cleanup_temp_files()

    def cancel_current_transfer(self):
        """取消当前传输"""
        self.cancel_transfer = True
        self.compression_manager.cancel_current_operation()

        # 如果有当前传输，尝试取消相关的SSH传输
        if self.current_transfer:
            plan = self.current_transfer
            if 'source_endpoint' in plan and hasattr(plan['source_endpoint'], 'ssh_client'):
                plan['source_endpoint'].ssh_client.cancel_current_transfer()
            if 'target_endpoint' in plan and hasattr(plan['target_endpoint'], 'ssh_client'):
                plan['target_endpoint'].ssh_client.cancel_current_transfer()

    def get_transfer_info(self, plan: Dict) -> Dict:
        """获取传输信息"""
        mode = plan['mode']
        source_name = plan['source_endpoint'].get_display_name()
        target_name = plan['target_endpoint'].get_display_name()

        info = {
            'mode': mode.value,
            'mode_description': self._get_mode_description(mode),
            'source_name': source_name,
            'target_name': target_name,
            'source_path': plan['source_path'],
            'target_path': plan['target_path'],
            'use_compression': plan['use_compression'],
            'is_directory': plan['is_directory'],
            'source_size': plan.get('source_size', 0)
        }

        return info

    def _get_mode_description(self, mode: TransferMode) -> str:
        """获取传输模式描述"""
        descriptions = {
            TransferMode.LOCAL_TO_LOCAL: "本地复制",
            TransferMode.LOCAL_TO_REMOTE: "上传到远程",
            TransferMode.REMOTE_TO_LOCAL: "从远程下载",
            TransferMode.REMOTE_TO_REMOTE: "服务器间传输"
        }
        return descriptions.get(mode, "未知模式")
