# 更新日志

## 版本 1.1.0 - 2025-07-07

### 新增功能

#### 🗂️ 远程目录浏览器
- **图形化目录浏览**: 连接服务器后，点击远程路径的"浏览"按钮可打开图形化目录浏览器
- **树形控件显示**: 使用Treeview控件显示目录结构，支持文件和目录的图标区分
- **详细信息显示**: 显示文件类型、大小、修改时间等详细信息
- **目录导航功能**: 
  - 双击目录进入子目录
  - 点击"上级"按钮返回父目录
  - 支持手动输入路径并跳转
  - 刷新当前目录内容
- **智能排序**: 目录优先显示，然后是文件，按名称排序
- **状态反馈**: 实时显示加载状态和操作结果

#### ✏️ 服务器配置编辑改进
- **自动填充配置**: 编辑服务器时，所有现有配置会自动填入编辑框
- **名称正确显示**: 修复了编辑时服务器名称不显示的问题
- **完整编辑支持**: 支持修改服务器的所有配置项

### 技术改进

#### 界面优化
- 新增 `RemoteDirectoryDialog` 类，提供专业的远程目录浏览体验
- 改进了服务器配置传递机制，确保编辑时数据完整性
- 优化了错误处理和用户反馈

#### 代码结构
- 增强了 `browse_remote_path` 方法，集成图形化浏览功能
- 修复了 `edit_server` 方法中的配置传递问题
- 改进了导入机制，支持相对和绝对导入

#### 打包优化
- 改进了打包脚本的错误处理
- 增加了权限问题的处理机制
- 优化了构建过程的稳定性

### 用户体验改进

#### 操作流程优化
1. **更直观的路径选择**: 不再需要手动输入远程路径，可以通过图形界面浏览选择
2. **减少错误输入**: 通过目录浏览器选择路径，避免路径输入错误
3. **提高效率**: 快速定位目标目录，提高备份操作效率

#### 界面友好性
- 目录和文件使用不同图标显示（📁 和 📄）
- 文件大小自动格式化显示（B, KB, MB, GB）
- 修改时间格式化显示
- 加载状态实时反馈

### 兼容性
- 保持与现有功能的完全兼容
- 支持所有原有的操作方式
- 新功能为可选使用，不影响原有工作流程

---

## 版本 1.0.0 - 2025-07-07

### 初始版本功能

#### 核心功能
- SSH连接管理
- 文件双向传输（上传/下载）
- 多服务器配置支持
- 密码加密存储
- 图形化用户界面

#### 技术特性
- 基于Paramiko的SSH实现
- Tkinter图形界面
- AES加密密码存储
- PyInstaller打包支持
- 虚拟环境管理

#### 基础功能
- 服务器配置管理
- 连接测试
- 文件传输进度显示
- 操作日志记录
- 传输取消功能

---

## 开发计划

### 未来版本规划

#### 版本 1.2.0 (计划中)
- [ ] 定时备份功能
- [ ] 备份计划管理
- [ ] 增量备份支持
- [ ] 压缩传输选项

#### 版本 1.3.0 (计划中)
- [ ] 多线程并发传输
- [ ] 断点续传功能
- [ ] 传输速度限制
- [ ] 网络状态监控

#### 版本 1.4.0 (计划中)
- [ ] 配置文件导入/导出
- [ ] 备份策略模板
- [ ] 邮件通知功能
- [ ] 详细统计报告

### 反馈和建议

如果您有任何建议或发现问题，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件反馈
- 参与项目讨论

感谢您使用远程文件备份工具！
